<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName git.oatechnologies.us
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/git_oatechnologies

    <Directory /var/www/git_oatechnologies/>
        Require all granted
        AllowOverride All
        Options FollowSymLinks MultiViews
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/git_oatechnologies_error.log
    CustomLog ${APACHE_LOG_DIR}/git_oatechnologies_access.log combined

    # Redirect HTTP to HTTPS
    RewriteEngine on
# Some rewrite rules in this file were disabled on your HTTPS site,
# because they have the potential to create redirection loops.

#     RewriteCond %{SERVER_NAME} =git.oatechnologies.us
#     RewriteRule ^ https://%{SERVER_NAME}%{REQUEST_URI} [END,NE,R=permanent]


SSLCertificateFile /etc/letsencrypt/live/git.oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/git.oatechnologies.us/privkey.pem
Include /etc/letsencrypt/options-ssl-apache.conf
</VirtualHost>
</IfModule>
